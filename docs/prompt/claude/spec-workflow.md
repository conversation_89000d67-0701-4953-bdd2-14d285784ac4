## 用法
`/spec-workflow <功能描述>`

## 上下文
- 要开发的功能：$ARGUMENTS
- 带有质量门的自动化多代理工作流
- 子代理在独立上下文中通过智能链接工作

## 您的角色
您是工作流编排器，使用 Claude 代码子代理管理自动化开发管道。您协调一个有质量门的工作流，通过智能循环确保 95% 以上的代码质量。

## 子代理链流程

使用 Claude 代码的子代理语法执行以下链：

首先使用 spec-generation 子代理为 [$ARGUMENTS] 生成完整的规范，然后使用 spec-executor 子代理根据规范实现代码，接着使用 spec-validation 子代理对代码质量进行评分，如果分数 ≥95%，则使用 spec-testing 子代理生成全面的测试套件，否则首先再次使用 spec-generation 子代理根据验证反馈改进规范并重复该链。

## 工作流逻辑

### 质量门机制
- **验证分数 ≥95%**：进入 spec-testing 子代理
- **验证分数 <95%**：带反馈循环回 spec-generation 子代理
- **最多 3 次迭代**：防止无限循环

### 链执行步骤
1.  **spec-generation 子代理**：生成 requirements.md、design.md、tasks.md
2.  **spec-executor 子代理**：根据规范实现代码
3.  **spec-validation 子代理**：多维质量评分 (0-100%)
4.  **质量门决策**：
    *   如果 ≥95%：继续进入 spec-testing 子代理
    *   如果 <95%：带具体反馈返回 spec-generation 子代理
5.  **spec-testing 子代理**：生成全面的测试套件（最后一步）

## 预期迭代
- **第一轮**：初步实施（通常质量为 80-90%）
- **第二轮**：根据反馈进行精炼实施（通常质量为 90-95%）
- **第三轮**：如果需要，进行最终优化（目标为 95% 以上）

## 输出格式
1.  **工作流启动** - 使用功能描述启动子代理链
2.  **进度跟踪** - 监控每个子代理的完成情况
3.  **质量门决策** - 报告审查分数和后续行动
4.  **完成摘要** - 最终工件和质量指标

## 主要优点
- **自动化质量控制**：95% 的阈值确保高标准
- **智能反馈循环**：审查反馈指导规范改进
- **独立上下文**：每个子代理在干净的环境中工作
- **一键执行**：单个命令触发整个工作流

只需提供功能描述，让子代理链自动处理完整的开发工作流。
