<desktop>
    <widgets id="ome_desktop_widgets_waittask" order='30'>待办事务表</widgets>
    <widgets id="ome_desktop_widgets_greenchannel" order='32'>绿色通道</widgets>
    <widgets id="ome_desktop_widgets_productinfo" order='40'>产品信息</widgets>
    <widgets id="ome_desktop_widgets_consultandpatch" order='50'>资讯及补丁</widgets>
    <widgets id="base_desktop_widgets_msgnotify" order='60'>系统消息</widgets>

    <permissions>
        <permission id="order_view_active">当前订单</permission>
        <permission id="order_view">历史订单</permission>
        <permission id="order_dispatch">订单调度</permission>
        <permission id="order_platform">平台自发订单</permission>
        <permission id="order_to_brush">设为特殊订单</permission>
        <permission id="order_back_buffer">退回暂存区</permission>
        <permission id="order_confirm" show='ome_roles:show_group' save='ome_roles:save_role'>订单确认</permission>
        <permission id="order_retrial">我的复审异常订单</permission>
        <permission id="order_reservation">待预约订单</permission>
        <permission id="order_abnormal">异常订单</permission>
        <permission id="order_lack">缺货列表</permission>
        <permission id="order_lack_supplier">缺货查看供应商</permission>
        <permission id="order_lack_purchase">缺货生成采购单</permission>
        <permission id="order_add">订单新建</permission>
        <permission id="order_groupon_index">团购订单导入列表</permission>
        <permission id="order_groupon_import">团购订单批量导入</permission>
        <permission id="order_export">订单导出</permission>
        <permission id="order_goback">订单退回</permission>
        <permission id="process_fast_consign" show='ome_roles:show_branch' save='ome_roles:save_role'>状态回写</permission>
        <permission id="batch_update_order">批量编辑订单</permission>
        <permission id="ome_platform_timeliness">平台发货时效管理</permission>

        <!--售后 begin-->
        <permission id="aftersale_return_apply">售后服务列表</permission>
        <permission id="aftersale_jingxiao_return_apply">平台自发售后服务列表</permission>
        <permission id="aftersale_return_add">新建售后服务</permission>
        <permission id="aftersale_return_edit">编辑售后服务</permission>
        <permission id="aftersale_return_check">审核中</permission>
        <permission id="aftersale_return_accept">接受申请</permission>
        <permission id="aftersale_return_refuse">拒绝</permission>
        <permission id="aftersale_return_apply_fail">失败售后申请</permission>
        <permission id="aftersale_return_batch">售后批量设置</permission>
        <permission id="aftersale_return_address">卖家地址库列表</permission>
        <permission id="aftersale_return_setting">售后自动审批设置</permission>
        <permission id="aftersale_return_rchange">退换货单列表</permission>
        <permission id="aftersale_jingxiao_return_rchange">平台自发退换货单列表</permission>
        <permission id="aftersale_rchange_add">新建退换货单</permission>
        <permission id="aftersale_rchange_edit">编辑退换货单</permission>
        <permission id="aftersale_rchange_check">审核退换货单</permission>
        <permission id="aftersale_rchange_recheck">反审核退换货单</permission>
        <permission id="aftersale_rchange_export">导出</permission>
        <permission id="aftersale_rchange_process">逐单质检</permission>
        <permission id="aftersale_rchange_sv">收货/质检</permission>
        <permission id="aftersale_compensate_record">直赔单</permission>
        <permission id="aftersale_sv_charge">唯一码查询</permission>
        <permission id="aftersale_servial_list">唯一码列表</permission>
        <permission id="aftersale_storagelife_list">保质期批次列表</permission>
        <permission id="aftersale_rchange_refuse">取消</permission>
        <permission id="aftersale_rchange_back">退回发货单</permission>
        <permission id="aftersale_rchange_back_process">追回发货单列表</permission>
        <permission id="aftersale_return_refund_apply">退款申请单列表</permission>
        <permission id="aftersale_add_refund">新建退款申请</permission>
        <!--售后 end-->

        <permission id="invoice_order_payment">收款单</permission>
        <permission id="invoice_order_refund">退款单</permission>
        <permission id="reback_delivery">撤销发货单</permission>
        <permission id="invoice_delivery">发货单管理</permission>
        <permission id="invoice_return">售后入库单</permission>
        <permission id="finance_payment_confirm">付款确认</permission>
        <permission id="finance_refund_confirm">退款确认</permission>
        <permission id="finance_jingxiao_refund_confirm">平台自发退款确认</permission>
        <permission id="setting_basic">系统设置</permission>
        <permission id="setting_print">打印模板管理</permission>
        <permission id="setting_payment_cfg">支付方式配置</permission>
        <permission id="setting_payment_account">收款账号管理</permission>
        <permission id="setting_shop">前端店铺管理</permission>
        <permission id="setting_operationorg">运营组织管理</permission>
        <permission id="setting_order_abnormal">订单异常类型设置</permission>
        <permission id="setting_product_problem">售后问题类型设置</permission>
        <permission id="setting_refund_reason">退款原因设置</permission>
        <permission id="setting_branch">仓库列表</permission>
        <permission id="ome_branch_flow_index">仓业务设置</permission>
        <permission id="setting_branch_type">仓库类型列表</permission>
        <permission id='setting_extrabranch'>外部仓库设置</permission>
        <permission id="setting_dly_corp">物流公司管理</permission>
        <permission id="ome_setting_iso_type">出入库类型设置</permission>
        <permission id="setting_preprocess_gift">淘宝赠品设置</permission>
        <permission id="setting_order_confirm_group">订单确认组管理</permission>
        <permission id="setting_api_log">同步日志管理</permission>

        <!-- 基础物料配置 -->
        <permission id="goods_brand">基础物料品牌</permission>
        <permission id="goods_type">基础物料类型</permission>
        <permission id="data_clear">清除数据</permission>
        <permission id="api_stock_log">库存同步管理</permission>
        <permission id="api_order_log">前端订单列表同步管理</permission>

        <!-- 导出权限 -->
        <permission id="goods_export">导出商品</permission>
        <permission id="goods_export_cost">导出成本价/重量模板</permission>
        <permission id="goods_import_cost">批量导入成本价/重量</permission>
        <permission id="pos_export">货位解绑</permission>
        <permission id="csv_pos_export">CSV文件导入解绑</permission>
        <permission id="bill_export">单据导出</permission>
        <permission id="finance_export">财务导出</permission>
        <permission id="process_receipts_print_export">发货单导出</permission>

        <!-- 操作日志start -->
        <permission id="operation_log">操作日志</permission>
        <permission id="login_log">登录日志</permission>
        <!-- 操作日志end -->

        <!-- 复审权限 -->
        <permission id="retrial_normal">商品变化订单</permission>
        <permission id="retrial_audit">价格异常订单</permission>
        <permission id="retrial_success">已复审订单</permission>
        <!-- 复审权限end -->

        <permission id="aftersale_branchset">退货仓库设置</permission>
        <permission id="aftersale_delivery_refuse">追回单据</permission>

        <!-- 赠品管理 -->
        <permission id="gift_logs">赠品发送记录</permission>
        <permission id="gift_setgift">赠品设置</permission>
        <!-- 赠品管理 end -->

        <!--客户管理-->
        <permission id="customer_list">客户列表</permission>

        <!-- 控制面板 -->
        <permission id="ome_freeze_queue">冻结队列表</permission>
        <permission id="ome_freeze_detail">冻结明细查询</permission>
        <permission id="add_reship_memo">添加退货单备注</permission>
    </permissions>

    <adminpanel group="desktop_user" permission="setting_operationorg" controller="admin_operationorg" display='true'>运营组织</adminpanel>
    <adminpanel group="desktop_setting" permission="setting" controller='admin_shop' action='shopnode' display='true'>网店节点</adminpanel>

    <workground name="日志管理" id="monitor_center"  order="1820" icon="icon-caozuorizhi-01">
        <menugroup name="接口日志" en="api-log">
            <menu controller='admin_api_log' action='index' permission='setting_api_log' display='true' order='600001'>日志列表</menu>
            <menu controller='admin_api_stock' action='index' permission='api_stock_log' display='true' order='600002'>库存同步列表</menu>
        </menugroup>
        <menugroup name="操作日志" en="operation-log">
            <menu controller='admin_operation_log' action='index' permission='operation_log' display='true' order='6000100'>操作日志</menu>
            <menu controller='admin_operation_log' action='login_list' permission='login_log' display='true' order='6000200'>登录日志</menu>
        </menugroup>
    </workground>

    <workground name="控制面板" id="adminpanel" order="1800" icon="icon-kongzhimianban-01">
        <menugroup name="管理员和权限" en="administrators-permissions">
            <menu controller='admin_operationorg' permission='setting_operationorg' display='true'>运营组织</menu>
        </menugroup>
    </workground>

    <workground name="订单管理" id="order_center" order="100" icon="icon-dingdanguanli-01">
        <menugroup name="订单查看" en="order-view">
            <menu controller='admin_order' action='active' permission='order_view_active' display='true'>当前订单</menu>
            <menu controller='admin_order' action='index' permission='order_view' display='true' order='1000200'>历史订单</menu>
            <menu controller='admin_order' action='dispatch' params='flt:buffer' permission='order_dispatch' display='true' order='1000300'>订单暂存区</menu>
            <menu controller='admin_order_platform' action='index' permission='order_platform' display='true' order='1000500'>平台自发订单</menu>
            <menu controller='' action='' permission='order_to_brush' display='false' order='1000350'>设置为特殊订单</menu>
        </menugroup>
        <menugroup name="订单调度" en="order-dispatch">
            <menu controller='admin_order' action='dispatch' params='flt:assigned' permission='order_dispatch' display='true' order='2000200'>已分派的订单</menu>
            <menu controller='admin_order' action='order_goback' params='action:goback' permission='order_goback' display='false' order='2000500'>订单退回</menu>
            <menu controller='admin_order' action='dispatch' params='flt:notassigned' permission='order_dispatch' display='true' order='2000300'>未分派的订单</menu>
            <menu controller='admin_order' action='dispatch' params='flt:buffer' permission='order_dispatch' display='false' order='2000400'>订单暂存区</menu>
        </menugroup>
        <menugroup name="订单处理" en="order-process">
            <menu controller='admin_order_reservation' action='index' permission='order_reservation' display='true' order='3000001'>待预约订单</menu>
            <menu controller='admin_order' action='confirm' params='flt:unmyown' permission='order_confirm' display='true' order='3000100'>我的待处理订单</menu>
            <menu controller='admin_order' action='confirm' params='flt:myown' permission='order_confirm' display='true' order='3000200'>我的已处理订单</menu>
            <menu controller='admin_order' action='retrial' permission='order_retrial' display='true' order='3000210'>我的复审异常订单</menu>
            <menu controller='admin_order' action='confirm' params='flt:ourgroup' permission='order_confirm' display='true' order='3000300'>本组的订单</menu>
            <menu controller='admin_order' action='order_buffer' permission='order_back_buffer' display='false' order='3000400'>退回暂存区</menu>
            <menu controller='admin_consign' action='fast_consign' permission='process_fast_consign' display='true' order='3000500'>状态回写</menu>
            <menu controller='admin_order' action='BatchUpdateOrder' permission='batch_update_order' display='false' order='3000600' >批量编辑订单</menu>
        </menugroup>
        <menugroup name="异常订单" en="abnormal-order">
            <menu controller='admin_order' action='abnormal' permission='order_abnormal' display='true' order='4000100'>异常订单</menu>
            <menu controller='admin_order_fail' action='index' permission='order_abnormal' display='true' order='4000200'>失败订单</menu>
        </menugroup>
        <menugroup name="复审订单" en="recheck-order">
            <menu controller='admin_order_retrial' action='index' permission='retrial_normal' display='true' order='4000500'>商品变化订单</menu>
            <menu controller='admin_order_retrial' action='audit' permission='retrial_audit' display='true' order='4000600'>价格异常订单</menu>
            <menu controller='admin_order_retrial' action='success' permission='retrial_success' display='true' order='4000700'>已复审订单</menu>
        </menugroup>
        <menugroup name="订单新建" en="order-add">
            <menu controller='admin_order' action='addNormalOrder' permission='order_add' display='false' order='5000100'>新建订单</menu>
            <menu controller='admin_order' action='export' permission='order_export' display='false' order='5000300'>订单导出</menu>
        </menugroup>
        <menugroup name="订单批量处理" en="order-batch-process">
            <menu controller='admin_order_groupon' action='index' permission='order_groupon_index' display='true' order='6000100'>团购订单导入列表</menu>
            <menu controller='admin_order_groupon' action='import' permission='order_groupon_import' display='true' order='6000200'>团购订单批量导入</menu>
        </menugroup>
        <menugroup name="缺货商品" en="stockout-product">
            <menu controller='admin_order_lack' action='search' permission='order_lack' display='true' order='7000100' >缺货商品</menu>
            <menu controller='admin_order_lack' action='index' permission='order_lack' display='false' order='7000200' >缺货商品</menu>
            <menu controller='admin_order_lack' action='supplier' permission='order_lack_supplier' display='false' order='7000300' >缺货商品</menu>
            <menu controller='admin_order_lack' action='createPurchase' permission='order_lack_purchase' display='false' order='7000400' >缺货生成采购单</menu>
            <menu controller='admin_order_lack' action='olist' permission='order_lack' display='true' order='7000500'>缺货订单</menu>
        </menugroup>
    </workground>

    <!--售后 addnew-->
    <workground name="售后管理" id="aftersale_center"  order="300" icon="icon-shouhouguanli-01">
        <menugroup name="售后" en="aftersale">
            <menu controller='admin_return' action='index' permission='aftersale_return_apply' display='true'>售后申请</menu>
            <menu controller='admin_return' action='jingxiao' permission='aftersale_jingxiao_return_apply' display='true' order='1000200'>平台自发</menu>
            <menu controller='admin_return_fail' action='index' permission='aftersale_return_apply_fail' display='true' order='1000100'>失败列表</menu>
            <menu controller='admin_return' action='add_return' permission='aftersale_return_add' display='false' order='1000102'>新建售后申请</menu>
            <menu controller='admin_return' action='edit' permission='aftersale_return_edit' display='false' order='1000103'>编辑售后申请</menu>
            <menu controller='admin_return' params='p[0]:2' action='save' permission='aftersale_return_check' display='false' order='1000104'>审核中</menu>
            <menu controller='admin_return' params='p[0]:3' action='save' permission='aftersale_return_accept' display='false' order='1000105'>接受申请</menu>
            <menu controller='admin_return' params='p[0]:5' action='save' permission='aftersale_return_refuse' display='false' order='1000106'>拒绝</menu>
        </menugroup>
        <menugroup name="退换货服务" en="return-exchange-service">
            <menu controller='admin_return_rchange' action='index' permission='aftersale_return_rchange' display='true' order='1000106'>退换货单</menu>
            <menu controller='admin_return_sv' action='index' permission='aftersale_rchange_process' display='true' order='4000103'>逐单质检</menu>
            <menu controller='admin_return_rchange' action='rchange' permission='aftersale_rchange_add' display='false' order='1000107'>新建退换货单</menu>
            <menu controller='admin_return_rchange' action='edit' permission='aftersale_rchange_edit' display='false' order='1000108'>编辑退换货单</menu>
            <menu controller='admin_return_rchange' action='check' permission='aftersale_rchange_check' display='false' order='1000109'>审核退换货单</menu>
            <menu controller='admin_return_rchange' action='anti_check' permission='aftersale_rchange_recheck' display='false' order='1000110'>反审核退换货单</menu>
            <menu controller='admin_return_rchange' action='index' params='action:export' permission='aftersale_rchange_export' display='false' order='1000111'>导出</menu>
            <menu controller='admin_return_rchange' action='do_cancel' permission='aftersale_rchange_refuse' display='false' order='1000113'>取消</menu>
            <menu controller='admin_return_rchange' action='jingxiao' permission='aftersale_jingxiao_return_rchange' display='true' order='4000200'>平台自发</menu>
            <menu controller='admin_reship' action='addmemo' permission='add_reship_memo' display='true' order='4000300'>添加退货单备注</menu>
        </menugroup>
        <menugroup name="唯一码" en="unique-code">
            <menu controller='admin_product_serial_history' action='search' permission='aftersale_sv_charge' display='true' order='1000116'>唯一码查询</menu>
            <menu controller='admin_product_serial_history' action='index' permission='aftersale_servial_list' display='true' order='1000117'>唯一码历史</menu>
        </menugroup>
        <menugroup name="保质期批次" en="shelf-life-batch">
            <menu controller='admin_product_storagelife_history' action='index' permission='aftersale_storagelife_list' display='true' order='1000117'>保质期批次历史</menu>
        </menugroup>
        <menugroup name="退款申请" en="refund-apply">
            <menu controller='admin_return_refund_apply' action='index' permission='aftersale_return_refund_apply' display='true' order='1000118'>退款申请单</menu>
            <menu controller='admin_return_refund_apply' action='add_refund' permission='aftersale_add_refund' display='false' order='1000119'>新建退款申请</menu>
        </menugroup>
        <menugroup name="退货收货" en="return-take-delivery">
            <menu controller='admin_return_sv' action='edit' permission='aftersale_rchange_sv' display='false' order='1000119'>质检</menu>
        </menugroup>
        <menugroup name="直赔单据" en="aftersale-compensate-record">
            <menu controller='admin_compensate_record' action='index' permission='aftersale_compensate_record' display='true' order='1001000'>直赔单</menu>
        </menugroup>
        <menugroup name="追回" en="aftersale-return-back">
            <menu controller='admin_return_rchange' action='do_cancel' permission='aftersale_rchange_refuse' display='false' order='1000113'>取消</menu>
            <menu controller='admin_delivery_back' action='index' permission='aftersale_rchange_back_process' display='true' order='1000121'>追回单据</menu>
        </menugroup>
    </workground>

    <workground name="财务管理" id="finance_center"  order="400" icon="icon-caiwuguanli-01">
        <menugroup name="订单财务" en="order-finance">
            <menu controller='admin_finance' action='index' permission='finance_payment_confirm' display='true'>付款确认</menu>
            <menu controller='admin_refund_apply' action='index' permission='finance_refund_confirm' display='true' order='3000102'>退款确认</menu>
            <menu  permission='finance_export' display='false'>财务导出</menu>
            <menu controller='admin_refund_apply' action='jingxiao' permission='finance_jingxiao_refund_confirm' display='true' order='3000103'>平台自发</menu>
        </menugroup>
    </workground>

    <workground name="基础档案" id="goods_manager"  order="600" icon="icon-jichudangan-01">
        <menugroup name="基础物料配置" en="basic-material-config">
            <menu controller='admin_goods_type' action='index' permission='goods_type' display='true' order='2000100'>基础物料类型</menu>
            <menu controller='admin_brand' action='index' permission='goods_brand' display='true' order='2000300'>基础物料品牌</menu>
        </menugroup>
        <menugroup name="仓库物流管理" en="branch-logistics-management">
            <menu controller='admin_branch' action='index' permission='setting_branch' display='true' order='4000100'>仓库列表</menu>
            <menu controller='admin_branchtype' action='index' permission='setting_branch_type' display='true' order='4000101'>仓库类型列表</menu>
            <menu controller='admin_branch_flow' action='index' permission='ome_branch_flow_index' display='true' order='4000102'>仓业务设置</menu>
            <menu controller='admin_extrabranch' action='index' permission='setting_extrabranch' display='true' order='4000300'>外部仓库设置</menu>
            <menu controller='admin_dly_corp' action='index' permission='setting_dly_corp' display='true' order='4000400'>物流公司管理</menu>
        </menugroup>
        <menugroup name="出入库管理" en="ome-iso-management">
            <menu controller='admin_iso_type' action='index' permission='ome_setting_iso_type' display='true' order='4100100'>出入库类型设置</menu>
        </menugroup>
        <menugroup name='客户管理' en="customer-management">
            <menu controller='admin_customer' action='index' permission='customer_list' display='true' order='5000000'>客户列表</menu>
        </menugroup>
    </workground>

    <workground name="单据报表" id="invoice_center"  order="1200" icon="icon-danjubaobiao-01">
        <menugroup name="销售单据" en="sales-bill">
            <menu controller='admin_payment' action='index' permission='invoice_order_payment' display='true' order='1000100'>收款单</menu>
            <menu controller='admin_refund' action='index' permission='invoice_order_refund' display='true' order='1000101'>退款单</menu>
            <menu permission='bill_export' display='false'>单据导出</menu>
        </menugroup>
        <menugroup name="仓库作业单据" en="wms-operation-documents">
            <menu controller='admin_delivery' action='index' permission='invoice_delivery' display='true' order='1000200'>发货单</menu>
            <menu controller='admin_delivery' action='export' permission='process_receipts_print_export' display='false' order='1000210'>单据-发货单导出</menu>
            <menu controller='admin_return' action='return_io' permission='invoice_return' display='true' order='1000201'>售后入库单</menu>
        </menugroup>
    </workground>

    <workground name="系统集成" id="channel_center" order="1400" icon="icon-xitongjicheng-01">
        <menugroup name="前端店铺" en="shop-management">
            <menu controller='admin_shop' action='index' permission='setting_shop' display='true' order='1001'>前端店铺管理</menu>
        </menugroup>
    </workground>

    <workground name="系统设置" id="setting_tools"  order="1100" icon="icon-xitongshezhi-01">
        <menugroup name="基本设置" en="basic-set">
            <menu controller='admin_setting' action='index' permission='setting_basic' display='true' order='1000101'>系统设置</menu>
            <menu controller='admin_print_otmpl' action='index' permission='setting_print' display='true' order='1000102'>打印模板管理</menu>
            <menu controller='admin_payment_cfg' action='index' permission='setting_payment_cfg' display='true' order='1000200'>支付方式配置</menu>
            <menu controller="admin_setting" action="set_collection_account" permission="setting_payment_account" display="true" order="1000400">收款账号管理</menu>
            <menu controller='admin_setting' action='abnormal' permission='setting_order_abnormal' display='true' order='1000400'>订单异常类型设置</menu>
            <menu controller='admin_platform_timeliness' action='index' permission='ome_platform_timeliness' display='true' order='1000500'>平台发货时效管理</menu>
        </menugroup>
        <menugroup name="售后设置" en="aftersale-set">
            <menu controller='admin_setting' action='product_problem' permission='setting_product_problem' display='true' order='1000500'>售后问题类型设置</menu>
            <menu controller='admin_refund_reason' action='index' permission='setting_refund_reason' display='true' order='1000550'>退款原因设置</menu>
            <menu controller='admin_return_address' action='index' permission='aftersale_return_address' display='true' order='1000600'>地址库列表</menu>
            <menu controller='admin_return_batch' action='index' permission='aftersale_return_batch' display='true' order='1000700'>批量操作设置</menu>
            <menu controller='admin_branchset' action='index' permission='aftersale_branchset' display='true' order='1000800'>退货仓库设置</menu>
            <menu controller='admin_return_setting' action='index' permission='aftersale_return_setting' display='true' order='1000900'>退换货自动审核设置</menu>
        </menugroup>
        <menugroup name="管理员管理" en="admin-administration">
            <menu controller='admin_group' action='index' permission='setting_order_confirm_group' display='true' order='4000100'>订单确认小组管理</menu>
        </menugroup>
        <menugroup name="赠品管理" en="gift-management">
            <menu controller="admin_crm_gift" action="logs" permission="gift_logs" display="true" order="7000300">赠品发送记录</menu>
            <menu controller="admin_crm_gift" action="setgift" permission="gift_setgift" display="true" order="7000400">赠品设置</menu>
            <menu controller='admin_preprocess_tbgift' action='index' permission='setting_preprocess_gift' display='true' order='7000500'>淘宝赠品管理</menu>
        </menugroup>
    </workground>

    <workground name="控制面板" id="adminpanel" order="1800" icon="icon-kongzhimianban-01">
        <menugroup name="其他"  en="other">
            <menu controller='admin_freeze_queue' action='index' params='' permission="ome_freeze_queue" order="10100" display='true'>冻结队列表</menu>
            <menu controller='admin_freeze_detail' action='index' params='' permission="ome_freeze_detail" order="10100" display='true'>冻结明细查询</menu>
        </menugroup>
    </workground>
</desktop>
