<?php

$db['platform_timeliness'] = array(
    'columns' => array(
        'id'   => array(
            'type'     => 'int unsigned',
            'extra'    => 'auto_increment',
            'pkey'     => true,
            'editable' => false,
            'label'    => '自增ID',
        ),
        'shop_type' => array(
            'type'              => 'varchar(32)',
            'label'             => '平台编码',
            'default'           => '',
            'is_title'          => true,
            'in_list'           => true,
            'default_in_list'   => true,
            'order'             => 10,
        ),
        'scene' => array(
            'type'              => 'varchar(32)',
            'label'             => '场景',
            'default'           => 'delivery',
            'in_list'           => true,
            'default_in_list'   => true,
            'order'             => 20,
            'comment'           => '可选值：delivery（发货）',
        ),
        'hour' => array(
            'type'              => 'int',
            'label'             => '时效时间',
            'default'           => 0,
            'in_list'           => true,
            'default_in_list'   => true,
            'order'             => 30,
        ),
        'memo' => array(
            'type'              => 'longtext',
            'label'             => '备注',
            'in_list'           => true,
            'default_in_list'   => true,
            'order'             => 40,
        ),
        'at_time'       => array(
            'type'            => 'TIMESTAMP',
            'label'           => '创建时间',
            'default'         => 'CURRENT_TIMESTAMP',
            'in_list'         => true,
            'default_in_list' => true,
            'order'           => 1000,
        ),
        'up_time'       => array(
            'type'            => 'TIMESTAMP',
            'label'           => '更新时间',
            'default'         => 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            'in_list'         => true,
            'default_in_list' => true,
            'order'           => 1010,
        ),
    ),
    'index'   => array(
        'idx_shop_type_scene' => array('columns' => array('shop_type', 'scene'), 'prefix' => 'UNIQUE'),
        'idx_at_time'       => array('columns' => array('at_time')),
        'idx_up_time'       => array('columns' => array('up_time')),
    ),
    'engine'  => 'innodb',
    'commit'  => '',
    'version' => 'Rev: 41996 $',
);