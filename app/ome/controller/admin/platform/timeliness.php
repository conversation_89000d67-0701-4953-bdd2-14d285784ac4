<?php

/**
 * 平台发货时效管理控制器
 * <AUTHOR>
 */
class ome_ctl_admin_platform_timeliness extends desktop_controller {

    var $name = "平台发货时效管理";
    var $workground = "channel_center";

    /**
     * 列表页
     */
    public function index() {
        $actions = array();
        $actions[] = array(
            'label'  => '新增',
            'href'   => $this->url . '&act=add',
            'target' => 'dialog::{width:770,height:450,title:\'新增\'}',
        );
        $params = array(
            'title' => '平台发货时效管理',
            'use_buildin_set_tag' => false,
            'use_buildin_filter' => false,
            'use_buildin_export' => false,
            'use_buildin_recycle' => true,
            'actions' => $actions,
            'orderBy' => 'id desc',
        );
        $this->finder('ome_mdl_platform_timeliness', $params);
    }

    public function add() {
        $this->pagedata['optShopType'] = ome_shop_type::get_shop_type();
        $this->display('admin/platform/timeliness/add.html');
    }

    public function save() {
        $data = $_POST;
        // 排除已经新建的（同平台编码+场景不能重复）
        $mdl = app::get('ome')->model('platform_timeliness');
        $filter = array(
            'shop_type' => $data['shop_type'],
            'scene' => $data['scene'],
        );
        // 编辑时排除自身
        if (!empty($data['id'])) {
            $filter['id|notin'] = array($data['id']);
        }
        $exists = $mdl->count($filter);
        if ($exists > 0) {
            $this->splash('fail', null, '该平台编码和场景的时效已存在，不能重复新建');
        }
        if ($mdl->save($data)) {
            $this->splash('success', $this->url, '新增成功');
        } else {
            $this->splash('fail', null, '新增失败');
        }
    }

    public function edit($id) {
        $mdl = app::get('ome')->model('platform_timeliness');
        $row = $mdl->db_dump(array('id'=>$id));
        if (!$row) {
            $this->splash('fail', null, '数据不存在');
        }
        $this->pagedata['optShopType'] = ome_shop_type::get_shop_type();
        $this->pagedata['data'] = $row;
        $this->display('admin/platform/timeliness/add.html');
    }

}