<?php

class ome_mdl_platform_timeliness extends dbeav_model{

    /**
     * 平台编码修饰器
     * 
     * @param string $shop_type 平台编码
     * @param array $list 数据列表
     * @param array $row 当前行数据
     * @return string
     */
    public function modifier_shop_type($shop_type, $list, $row)
    {
        $shopTypeList = ome_shop_type::get_shop_type();
        return $shopTypeList[$shop_type] ? : $shop_type;
    }

    /**
     * 场景修饰器
     * 
     * @param string $scene 场景
     * @param array $list 数据列表
     * @param array $row 当前行数据
     * @return string
     */
    public function modifier_scene($scene, $list, $row)
    {
        $sceneMap = array(
            'delivery' => '发货'
        );

        return isset($sceneMap[$scene]) ? $sceneMap[$scene] : $scene;
    }

} 