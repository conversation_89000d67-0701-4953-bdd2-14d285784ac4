<?php

/**
 * ============================
 * @Author:   miele
 * @describe: ESB服务类
 * ============================
 */
class miele_esb_service extends miele_abstract {

    public $request_url = ESB_URL;

    public $timeout = 30;

    public $original_bn = '';
    
    /**
     * 发送请求到ESB
     * @return array
     */
    public function request($url, $params) {
        
        // 默认header
        $headers = array(
            'Content-Type: application/x-www-form-urlencoded',
        );
        
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));

            $response_body = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($http_code == 200) {
                $this->write_log($params['method'], $params, json_decode($response_body, true));
            } else {
                $this->write_log($params['method'], $params, array('rsp' => 'fail', 'msg' => $response_body));
            }
            
            if ($curl_error) {
                throw new Exception("CURL错误: " . $curl_error);
            }
            
            if ($http_code == 200) {
                return json_decode($response_body, true);
            } else {
                throw new Exception("ESB请求失败: HTTP " . $http_code . " - " . $response_body);
            }
        } catch (Exception $e) {
            return array('rsp' => 'fail', 'msg' => $e->getMessage());
        }
    }
    
    /**
     * 发送奇门接口请求
     * @param string $method 奇门API名称
     * @param array $data 请求数据
     * @param array $headers 自定义header
     * @return array
     */
    public function qimenRequest($method, $data = array()) {

        $params = array(
            'method' => $method,
            'node_id' => base_shopnode::node_id('ome'), // 节点ID
        );

        $params = array_merge($params, $data);

        $sign = $this->gen_sign($params);
        $params['sign'] = $sign;

        return $this->request($this->request_url, $params);
    }

    public function gen_sign($params)
    {
        $token = base_certificate::token();
        return strtoupper(md5(strtoupper(md5(self::assemble($params))).$token));
    }

    public function assemble($params)
    {
        if (!is_array($params)) {
            return null;
        }

        ksort($params, SORT_STRING);
        $sign = '';
        foreach ($params as $key => $val) {
            if (is_null($val)) {
                continue;
            }

            if (is_bool($val)) {
                $val = ($val) ? 1 : 0;
            }

            $sign .= $key . (is_array($val) ? self::assemble($val) : $val);
        }
        return $sign;
    }  

    /**
     * 监控
     *
     * @return void
     * <AUTHOR>
    private function write_log($method, $data, $response)
    {
        $apiLogModel = app::get('ome')->model('api_log');
        $log_id = uniqid();
        $kafkaData = array(
            'log_id'      => $log_id,
            'url' => $this->request_url,
            'worker'      => $method,
            'task_name'   => '推送ESB:'.$method,
            'original_bn' => $this->original_bn ? $this->original_bn : $method,
            'status'      => $response['rsp'] == 'succ' ? 'success' : 'fail',
            'createtime'  => time(),
            'api_type'    => 'request',
            'response'    => json_encode($response),
            'params' => json_encode($data),
            'msg' => $response['msg'] ?? $response['error_msg'],
            'msg_id' => $response['request_id'] ?? $data['sign'], 
        );
        $apiLogModel->insert($kafkaData);
    }

} 