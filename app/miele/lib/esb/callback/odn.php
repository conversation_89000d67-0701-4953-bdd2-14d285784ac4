<?php
/**
 * ============================
 * @Author:   AI
 * @describe: ODN回调处理类
 * 基于回调接口文档：status(E/S), message, dnS4(SAP DN号)
 * ODN创建、发货完成、取消回调使用统一接口
 * ============================
 */
class miele_esb_callback_odn extends miele_esb_service {

    /**
     * 第一步：根据status，deliveryDate 更新SO单的sap预约状态和预约时间
     * @param array $params 接口参数
     * @return array 处理结果 {rsp: E/S, msg: string}
     */
    private function _updateSoReservationStatus($params) {
        try {
            // 根据soS4查询SO单
            $soModel = app::get('miele')->model('sap_so');
            $soInfo = $soModel->dump(['sap_so_bn' => $params['soS4']], '*');

            if (empty($soInfo)) {
                throw new Exception('未找到SO单：' . $params['soS4']);
            }

            // 根据status更新预约状态
            $updateData = [];

            if ($params['status'] == 'Y') {
                // 预约成功
                $updateData['sap_rsv_status'] = '1';
                $updateData['sap_rsv_reason'] = '预约成功';

                // 如果有deliveryDate，更新预约时间
                if (!empty($params['deliveryDate'])) {
                    $deliveryTimestamp = $this->_parseDeliveryDate($params['deliveryDate']);
                    if ($deliveryTimestamp) {
                        $updateData['sap_rsv_time'] = $deliveryTimestamp;
                    }
                }
            } else {
                // 预约失败
                $updateData['sap_rsv_status'] = '2';
                $updateData['sap_rsv_reason'] = 'SAP返回预约失败状态';
                $updateData['sap_rsv_time'] = time();

                // 预约失败，需要通过service通知OMS进行重新预约
                $reservationService = kernel::single('ome_order_reservation');
                $reReservationResult = $reservationService->againReservation($soInfo['order_id'], 'sap_reservation_failed');
                if ($reReservationResult['rsp'] == 'fail') {
                    throw new Exception('重新预约失败：' . $reReservationResult['msg']);
                }
            }

            $updateData['up_time'] = time();

            // 更新SO单
            $result = $soModel->update($updateData, ['id' => $soInfo['id']]);
            if (!$result) {
                throw new Exception('SO单预约状态更新失败');
            }

            // 记录操作日志
            $opObj = app::get('ome')->model('operation_log');
            $opObj->write_log(
                'so_reservation_update@miele',
                $soInfo['id'],
                sprintf('SAP预约状态更新：%s，预约日期：%s，原因：%s',
                    $params['status'] == 'Y' ? '成功' : '失败',
                    $params['deliveryDate'] ?? '无',
                    $updateData['sap_rsv_reason']
                )
            );

            return [
                'rsp' => 'succ',
                'msg' => 'SO单预约状态更新成功',
                'so_info' => $soInfo
            ];

        } catch (Exception $e) {
            return [
                'rsp' => 'fail',
                'msg' => 'SO单预约状态更新失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 解析deliveryDate格式（YYYYMMDD）为时间戳
     * @param string $deliveryDate
     * @return int|false
     */
    private function _parseDeliveryDate($deliveryDate) {
        if (strlen($deliveryDate) != 8) {
            return false;
        }

        $year = substr($deliveryDate, 0, 4);
        $month = substr($deliveryDate, 4, 2);
        $day = substr($deliveryDate, 6, 2);

        return strtotime($year . '-' . $month . '-' . $day);
    }

    /**
     * 第二步：miele_service_delivery_odn setSource 为sap和接口推送的参数
     * @param array $params 接口参数
     * @return array 处理结果 {rsp: E/S, msg: string}
     */
    private function _setOdnServiceSource($params) {
        // 设置ODN服务的source为sap
        $odnService = kernel::single('miele_service_delivery_odn');
        $odnService->setSource('sap');
        return true;
    }

    /**
     * 第三步：分析omeauto_auto_combine:mkDelivery 方法和调用mkDelivery方法的逻辑
     * 检查组织参数，创建发货单，发货单的商品和数量需要是item
     * @param array $params 接口参数
     * @return array 处理结果 {rsp: E/S, msg: string, delivery_id?: int, order_ids?: array}
     */
    private function _createDeliveryByMkDelivery($params) {

        // todo 需要使用oms预约表的手机号，地址

        try {
            // 根据itemSO查询sap_so_items表获取订单明细信息
            $soItemsModel = app::get('miele')->model('sap_so_items');
            $orderIds = [];
            $orderInfo = null;
            $soItems = [];

            foreach ($params['item'] as $item) {
                if (empty($item['itemSO'])) {
                    throw new Exception('itemSO不能为空');
                }

                // 根据itemSO查询sap_so_items表
                $soItem = $soItemsModel->dump(['item_line_no' => $item['itemSO']], '*');
                if (empty($soItem)) {
                    throw new Exception('未找到itemSO=' . $item['itemSO'] . '的SO明细记录');
                }

                $soItems[] = $soItem;

                // 获取订单ID
                if (!in_array($soItem['order_id'], $orderIds)) {
                    $orderIds[] = $soItem['order_id'];
                }

                // 获取订单主信息（只需要获取一次）
                if ($orderInfo === null) {
                    $orderInfo = app::get('ome')->model('orders')->dump(['order_id' => $soItem['order_id']], '*');
                    if (empty($orderInfo)) {
                        throw new Exception('未找到订单ID=' . $soItem['order_id'] . '的订单记录');
                    }
                }
            }

            // 构建consignee参数 - 直接使用订单的consignee字段
            $consignee = $orderInfo['consignee'] ?? [];

            // 根据forwardAgent查询物流公司获取logiId
            $logiId = null;
            if (!empty($params['forwardAgent'])) {
                $dlyCorpModel = app::get('ome')->model('dly_corp');
                $corpInfo = $dlyCorpModel->dump(['type' => $params['forwardAgent']], 'corp_id');
                if (!empty($corpInfo)) {
                    $logiId = $corpInfo['corp_id'];
                }
            } else {
                // 如果没有指定物流公司，使用订单原有的物流公司
                $logiId = $orderInfo['logi_id'] ?? null;
            }

            // 根据plant查询仓库信息获取branch_id
            if (!empty($params['item'][0]['plant'])) {
                $branchModel = app::get('ome')->model('branch');
                $branchInfo = $branchModel->dump(['branch_bn' => $params['item'][0]['plant']], 'branch_id');
                if (!empty($branchInfo)) {
                    $consignee['branch_id'] = $branchInfo['branch_id'];
                }
            }

            // 验证必要参数
            if ($logiId === null) {
                throw new Exception('未找到有效的物流公司信息');
            }

            // 构建splitting_product参数，只发货指定的商品和数量
            $splitting_product = [];
            foreach ($soItems as $soItem) {
                // 查找对应的item参数
                $itemParam = null;
                foreach ($params['item'] as $item) {
                    if ($item['itemSO'] == $soItem['item_line_no']) {
                        $itemParam = $item;
                        break;
                    }
                }

                if ($itemParam) {
                    $splitting_product[] = [
                        'order_id' => $soItem['order_id'],
                        'oid' => $soItem['oid'],
                        'product_id' => $soItem['product_id'],
                        'bn' => $soItem['bn'],
                        'nums' => $itemParam['quantity'] ?? $soItem['nums'], // 使用接口传入的数量
                    ];
                }
            }

            // 调用mkDelivery方法生成发货单
            $combineObj = kernel::single('omeauto_auto_combine');
            $errmsg = '';
            $split_auto = [];
            $result = $combineObj->mkDelivery($orderIds, $consignee, $logiId, $splitting_product, $errmsg, $split_auto);

            if (!$result) {
                throw new Exception('mkDelivery调用失败：' . $errmsg);
            }

            // 获取生成的发货单ID
            $deliveryModel = app::get('ome')->model('delivery_order');
            $deliveryInfo = $deliveryModel->dump(['order_id' => $orderIds[0]], 'delivery_id');

            if (empty($deliveryInfo)) {
                throw new Exception('未找到生成的发货单');
            }

            return [
                'rsp' => 'succ',
                'msg' => '发货单生成成功',
                'delivery_id' => $deliveryInfo['delivery_id'],
                'order_ids' => $orderIds,
                'so_items' => $soItems
            ];

        } catch (Exception $e) {
            return [
                'rsp' => 'fail',
                'msg' => '发货单生成失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 第四步：更新ODN单据，itemDN是ODN明细的行号，根据第二步保存的时候进行调整逻辑
     * @param int $deliveryId 发货单ID
     * @param array $params 接口参数
     * @return array 处理结果 {rsp: E/S, msg: string}
     */
    private function _updateOdnWithItemDN($deliveryId, $params) {
        try {
            // 通过发货单ID保存ODN数据
            $odnService = kernel::single('miele_service_delivery_odn');
            $result = $odnService->saveOdnByDeliveryId($deliveryId);

            if ($result['rsp'] != 'succ') {
                throw new Exception('ODN数据保存失败：' . $result['msg']);
            }

            $odnId = $result['data']['odn_id'];

            // 更新ODN明细的itemDN（SAP DN行号）
            $odnItemsModel = app::get('miele')->model('sap_odn_items');

            foreach ($params['item'] as $item) {
                if (empty($item['itemSO']) || empty($item['itemDN'])) {
                    continue;
                }

                // 根据itemSO查询对应的SO明细
                $soItemsModel = app::get('miele')->model('sap_so_items');
                $soItem = $soItemsModel->dump(['item_line_no' => $item['itemSO']], '*');

                if (empty($soItem)) {
                    continue;
                }

                // 根据商品编码和ODN ID查找ODN明细
                $odnItem = $odnItemsModel->dump([
                    'odn_id' => $odnId,
                    'bn' => $soItem['bn']
                ], '*');

                if ($odnItem) {
                    // 更新ODN明细的item_line_no为itemDN
                    $updateData = [
                        'item_line_no' => $item['itemDN'],
                        'up_time' => time(),
                    ];

                    $odnItemsModel->update($updateData, ['id' => $odnItem['id']]);
                }
            }

            // 更新ODN主表的SAP DN号
            $odnModel = app::get('miele')->model('sap_odn');
            $odnModel->update([
                'sap_odn_bn' => $params['dnS4'],
                'up_time' => time(),
            ], ['id' => $odnId]);

            return [
                'rsp' => 'succ',
                'msg' => 'ODN单据更新成功',
                'odn_id' => $odnId
            ];

        } catch (Exception $e) {
            return [
                'rsp' => 'fail',
                'msg' => 'ODN单据更新失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * SAP创建DN单及预约接口处理（优化版本）
     * 入参参考：AI/requirement/OMS开放接口文档V1.6.md miele.sap.odn.add 方法
     * @param array $params 接口参数
     * @return array 处理结果 {rsp: E/S, msg: string}
     */
    public function addOdnBySap($params) {
        try {
            // 验证必要参数
            if (empty($params['soS4']) || empty($params['dnS4']) || empty($params['item'])) {
                return [
                    'rsp' => 'fail',
                    'msg' => '缺少必要参数：soS4、dnS4或item'
                ];
            }

            // 根据soS4查询SO单获取主键ID用于日志记录
            $soModel = app::get('miele')->model('sap_so');
            $soInfo = $soModel->dump(['sap_so_bn' => $params['soS4']], 'id');
            $soId = $soInfo['id'] ?? 0;

            // 记录接口调用日志
            $opObj = app::get('ome')->model('operation_log');
            $opObj->write_log(
                'odn_add_by_sap@miele',
                $soId, // 使用SO表主键
                sprintf('SAP创建DN单接口调用：SO号=%s，DN号=%s，预约状态=%s，预约日期=%s，明细数量=%d',
                    $params['soS4'],
                    $params['dnS4'],
                    $params['status'] ?? 'N',
                    $params['deliveryDate'] ?? '',
                    count($params['item'])
                )
            );

            // 第一步：根据status，deliveryDate 更新SO单的sap预约状态和预约时间
            $step1Result = $this->_updateSoReservationStatus($params);
            if ($step1Result['rsp'] != 'succ') {
                return $step1Result;
            }

            // 第二步：miele_service_delivery_odn setSource 为sap和接口推送的参数
            $this->_setOdnServiceSource($params);

            // 第三步：分析omeauto_auto_combine:mkDelivery 方法和调用mkDelivery方法的逻辑
            $step3Result = $this->_createDeliveryByMkDelivery($params);
            if ($step3Result['rsp'] != 'succ') {
                return $step3Result;
            }

            $deliveryId = $step3Result['delivery_id'];
            $orderIds = $step3Result['order_ids'];

            // 第四步：更新ODN单据，itemDN是ODN明细的行号，根据第二步保存的时候进行调整逻辑
            $step4Result = $this->_updateOdnWithItemDN($deliveryId, $params);
            if ($step4Result['rsp'] != 'succ') {
                return $step4Result;
            }

            // 记录成功日志
            $opObj->write_log(
                'odn_add_by_sap@miele',
                $soId, // 使用SO表主键
                sprintf('SAP创建DN单成功：SO号=%s，DN号=%s，发货单ID=%d，订单数量=%d',
                    $params['soS4'],
                    $params['dnS4'],
                    $deliveryId,
                    count($orderIds)
                )
            );

            return [
                'rsp' => 'succ',
                'msg' => 'DN单创建成功'
            ];

        } catch (Exception $e) {
            // 记录异常日志
            $opObj = app::get('ome')->model('operation_log');
            // 获取SO主键ID用于异常日志
            $soModel = app::get('miele')->model('sap_so');
            $soInfo = $soModel->dump(['sap_so_bn' => $params['soS4'] ?? ''], 'id');
            $soId = $soInfo['id'] ?? 0;

            $opObj->write_log(
                'odn_add_by_sap@miele',
                $soId, // 使用SO表主键
                sprintf('SAP创建DN单异常：SO号=%s，DN号=%s，异常信息=%s',
                    $params['soS4'] ?? '',
                    $params['dnS4'] ?? '',
                    $e->getMessage()
                )
            );

            return [
                'rsp' => 'fail',
                'msg' => 'DN单创建异常：' . $e->getMessage()
            ];
        }
    }
    
    /**
     * ODN创建回调处理
     * @param array $callbackData 回调数据 {status: E/S, message: string, otherSystemODN: string, dnS4?: string, item?: array}
     * @return array 处理结果 {status: E/S, message: string}
     */
    public function addCallback($callbackData) {
        try {
            $odnMdl = app::get('miele')->model('sap_odn');
            
            // 根据回调接口文档，status: E=失败, S=成功
            if ($callbackData['status'] == 'S') {
                $updateData = [
                    'sap_add_status' => 'succ',
                    'sap_odn_bn' => $callbackData['dnS4'] ?? '', // SAP DN号
                    'sap_add_msg' => $callbackData['message'] ?? '创建成功',
                ];
            } else {
                $updateData = [
                    'sap_add_status' => 'fail',
                    'sap_add_msg' => $callbackData['message'] ?? '创建失败',
                ];
            }
            
            // 按照 docs/cheatsheet/operation_log_template.md 规范记录操作日志
            $logMessage = sprintf('ODN创建回调：%s，DN号：%s，消息：%s',
                ($callbackData['status'] == 'S' ? '成功' : '失败'),
                $callbackData['dnS4'] ?? '',
                htmlspecialchars($callbackData['message'] ?? '')
            );
            
            // 如果包含明细信息，在日志中记录明细行数量
            if (!empty($callbackData['item']) && is_array($callbackData['item'])) {
                $logMessage .= sprintf('，明细行数：%d', count($callbackData['item']));
            }

            $odnInfo = $odnMdl->dump(['delivery_bn' => $callbackData['otherSystemODN']], '*');
            if (empty($odnInfo)) {
                return $this->error('ODN不存在');
            }

            $odnMdl->update($updateData, ['id' => $odnInfo['id']]);

            // 处理明细行更新（如果回调包含明细信息）
            if ($callbackData['status'] == 'S' && !empty($callbackData['item']) && is_array($callbackData['item'])) {
                $this->_updateOdnItems($odnInfo['id'], $callbackData['item']);
            }

            // 更新其他状态
            kernel::single('miele_service_delivery_odn')->updateSapStatusByDeliveryId($odnInfo['delivery_id']);

            $opObj = app::get('ome')->model('operation_log');
            $opObj->write_log(
                'odn_create_callback@miele',
                $odnInfo['id'], // 使用ODN表主键
                $logMessage
            );

            return $this->succ('ODN创建回调处理成功');
        } catch (Exception $e) {
            return $this->error('ODN创建回调处理失败: ' . $e->getMessage());
        }
    }
    
    /**
     * ODN发货完成回调处理
     * @param array $callbackData 回调数据 {status: E/S, message: string, dnS4: string}
     * @return array 处理结果 {rsp: succ/fail, msg: string}
     */
    public function consignCallback($callbackData) {
        try {
            $odnMdl = app::get('miele')->model('sap_odn');
            
            // 验证必要参数
            if (empty($callbackData['dnS4'])) {
                return [
                    'rsp' => 'fail',
                    'msg' => '缺少必要参数：dnS4'
                ];
            }
            
            // 根据回调接口文档，status: E=失败, S=成功
            if ($callbackData['status'] == 'S') {
                $updateData = [
                    'sap_consign_status' => 'succ',
                    'sap_consign_msg' => $callbackData['message'] ?? '发货成功',
                ];
            } else {
                $updateData = [
                    'sap_consign_status' => 'fail',
                    'sap_consign_msg' => $callbackData['message'] ?? '发货失败',
                ];
            }
            
            // 根据 dnS4 查找对应的ODN记录
            $odnInfo = $odnMdl->dump(['sap_odn_bn' => $callbackData['dnS4']], '*');
            
            if (empty($odnInfo)) {
                return [
                    'rsp' => 'fail',
                    'msg' => sprintf('未找到DN号为 %s 的ODN记录', $callbackData['dnS4'])
                ];
            }
            
            // 按照 docs/cheatsheet/operation_log_template.md 规范记录操作日志
            $opObj = app::get('ome')->model('operation_log');
            $opObj->write_log(
                'odn_consign_callback@miele',
                $odnInfo['id'], // 使用ODN表主键
                sprintf('ODN发货回调：%s，DN号：%s，消息：%s',
                    ($callbackData['status'] == 'S' ? '成功' : '失败'),
                    $callbackData['dnS4'],
                    htmlspecialchars($callbackData['message'] ?? '')
                )
            );
            
            $odnMdl->update($updateData, ['id' => $odnInfo['id']]);
            
            return [
                'rsp' => 'succ',
                'msg' => 'ODN发货回调处理成功'
            ];
        } catch (Exception $e) {
            return [
                'rsp' => 'fail',
                'msg' => 'ODN发货回调处理失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * ODN取消回调处理
     * @param array $callbackData 回调数据 {status: E/S, message: string, dnS4: string}
     * @return array 处理结果 {rsp: succ/fail, msg: string}
     */
    public function cancelCallback($callbackData) {
        try {
            $odnMdl = app::get('miele')->model('sap_odn');
            
            // 验证必要参数
            if (empty($callbackData['dnS4'])) {
                return [
                    'rsp' => 'fail',
                    'msg' => '缺少必要参数：dnS4'
                ];
            }
            
            // 根据回调接口文档，status: E=失败, S=成功
            if ($callbackData['status'] == 'S') {
                $updateData = [
                    'sap_cancel_status' => 'succ',
                    'sap_cancel_msg' => $callbackData['message'] ?? '取消成功',
                ];
            } else {
                $updateData = [
                    'sap_cancel_status' => 'fail',
                    'sap_cancel_msg' => $callbackData['message'] ?? '取消失败',
                ];
            }
            
            // 根据 dnS4 查找对应的ODN记录
            $odnInfo = $odnMdl->dump(['sap_odn_bn' => $callbackData['dnS4']], '*');
            
            if (empty($odnInfo)) {
                return [
                    'rsp' => 'fail',
                    'msg' => sprintf('未找到DN号为 %s 的ODN记录', $callbackData['dnS4'])
                ];
            }
            
            // 按照 docs/cheatsheet/operation_log_template.md 规范记录操作日志
            $opObj = app::get('ome')->model('operation_log');
            $opObj->write_log(
                'odn_cancel_callback@miele',
                $odnInfo['id'], // 使用ODN表主键
                sprintf('ODN取消回调：%s，DN号：%s，消息：%s',
                    ($callbackData['status'] == 'S' ? '成功' : '失败'),
                    $callbackData['dnS4'],
                    htmlspecialchars($callbackData['message'] ?? '')
                )
            );
            
            $odnMdl->update($updateData, ['id' => $odnInfo['id']]);

            return [
                'rsp' => 'succ',
                'msg' => 'ODN取消回调处理成功'
            ];
        } catch (Exception $e) {
            return [
                'rsp' => 'fail',
                'msg' => 'ODN取消回调处理失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 更新ODN明细行的SAP行号
     * @param int $odnId ODN ID
     * @param array $items 明细数组 [{itemOMS: int, itemS4: string, material: string}]
     * @return bool
     */
    private function _updateOdnItems($odnId, $items) {
        $odnItemsModel = app::get('miele')->model('sap_odn_items');
        
        foreach ($items as $item) {
            // 验证必要字段
            if (empty($item['itemOMS']) || empty($item['itemS4'])) {
                continue;
            }
            
            // 根据itemOMS（ODN明细行ID）更新item_line_no（SAP DN行号）
            $updateData = [
                'item_line_no' => $item['itemS4'],
                'up_time' => time(),
            ];
            
            $result = $odnItemsModel->update($updateData, [
                'id' => $item['itemOMS'],
                'odn_id' => $odnId
            ]);
            
            if (!$result) {
                throw new Exception("更新明细行失败，itemOMS: {$item['itemOMS']}, itemS4: {$item['itemS4']}");
            }
        }
        
        return true;
    }
}
