<?php
class miele_openapi_conf
{
    public function getMethods()
    {
        return array(
            'miele.so' => array(
                'label'   => 'SO单',
                'methods' => array(
                    'updateReserve' => '预约日期更新',
                    'syncCallback' => 'SO创建回调',
                ),
                'group' => 'miele',
            ),
            'miele.odn' => array(
                'label'   => 'ODN单',
                'methods' => array(
                    'add' => '创建ODN单',
                    'addCallback' => 'ODN创建回调',
                    'cancelCallback' => 'ODN取消回调',
                    'consignCallback' => 'ODN发货回调',
                ),
                'group' => 'miele',
            ),
            'miele.debitnote' => array(
                'label'   => 'debitnote单',
                'methods' => array(
                    'syncCallback' => '借贷创建回调',
                ),
                'group' => 'miele',
            ),
            'miele.goods' => array(
                'label'   => '商品同步',
                'methods' => array(
                    'add' => '商品新增',
                ),
                'group' => 'miele',
            ),
            'miele.returnorder' => array(
                'label'   => '退货单',
                'methods' => array(
                    'addCallback' => '退货单创建回调',
                ),
                'group' => 'miele',
            ),
        );
    }
}