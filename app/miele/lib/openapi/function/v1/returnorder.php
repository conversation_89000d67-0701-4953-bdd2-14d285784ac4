<?php
/**
 * 退货单OpenAPI功能实现
 * 类名: miele_openapi_function_v1_reship
 * 文件: app/miele/lib/openapi/function/v1/reship.php
 */
class miele_openapi_function_v1_returnorder extends openapi_api_function_abstract implements openapi_api_function_interface {

    /**
     * 退货单创建回调
     * @param array $params 回调参数
     * @param string $code 错误码
     * @param string $sub_msg 错误消息
     * @return array 处理结果
     */
    function addCallback($params, &$code, &$sub_msg) {
        $res = kernel::single('miele_esb_callback_reship')->addCallback($params);
        return $res;
    }
}
