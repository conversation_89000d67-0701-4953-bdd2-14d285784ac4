<?php

class miele_openapi_params_v1_debitnote extends openapi_api_params_abstract implements openapi_api_params_interface
{

    public function checkParams($method, $params, &$sub_msg)
    {
        if(parent::checkParams($method,$params,$sub_msg)){
            return true;
        }else{
            return false;
        }
    }

    public function getAppParams($method)
    {
        $params = array(
            'syncCallback'=>array(
                'status'=>array('type'=>'string','required'=>'true','name'=>'状态','desc'=>'状态。可选值：E（ 处理失败），S（处理成功）'),
                'message'=>array('type'=>'string','required'=>'false','name'=>'信息','desc'=>'message'),
                'otherSystemSO'=>array('type'=>'string','required'=>'true','name'=>'平台借贷项订单号','desc'=>'平台借贷项订单号'),
                's4SO'=>array('type'=>'string','required'=>'true','name'=>'SAP借贷项订单号','desc'=>'SAP借贷项订单号'),
                // 'item'=>array('type'=>'string','required'=>'true','name'=>'行明细','desc'=>'行明细'),
            ),
        );

        return $params[$method];
    }

    public function description($method)
    {
        $desccription = array(
            'syncCallback' => array('name' => '借贷创建回调', 'description' => '借贷创建回调(OMS发起的接收SAP回调的接口)'),
        );
        return $desccription[$method];
    }
}