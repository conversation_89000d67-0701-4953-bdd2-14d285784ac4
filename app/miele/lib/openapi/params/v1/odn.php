<?php

class miele_openapi_params_v1_odn extends openapi_api_params_abstract implements openapi_api_params_interface
{

    public function checkParams($method, $params, &$sub_msg)
    {
        if(parent::checkParams($method,$params,$sub_msg)){
            return true;
        }else{
            return false;
        }
    }

    public function getAppParams($method)
    {
        $params = array(
            'add'=>array(
                'status'=>array('type'=>'string','required'=>'true','name'=>'状态','desc'=>'是否预约成功的状态。可选值：Y（成功），N（失败）'),
                'soS4'=>array('type'=>'string','required'=>'true','name'=>'SAP订单','desc'=>'SAP订单'),
                'dnS4'=>array('type'=>'string','required'=>'true','name'=>'SAP交货单号','desc'=>'SAP交货单号'),
                'deliveryDate'=>array('type'=>'string','required'=>'true','name'=>'预约成功的发货日期','desc'=>'格式：20250529；预约成功的发货日期'),
                'forwardAgent'=>array('type'=>'string','required'=>'true','name'=>'运输公司编码','desc'=>'运输公司编码'),
                // 'item'=>array('type'=>'string','required'=>'true','name'=>'行明细','desc'=>'行明细'),
            ),
            'addCallback'=>array(
                'status'=>array('type'=>'string','required'=>'true','name'=>'状态','desc'=>'状态。可选值：E（ 处理失败），S（处理成功）'),
                'message'=>array('type'=>'string','required'=>'false','name'=>'信息','desc'=>'message'),
                'otherSystemODN'=>array('type'=>'string','required'=>'true','name'=>'OMS 发货单编号','desc'=>'otherSystemODN'),
                'dnS4'=>array('type'=>'string','required'=>'true','name'=>'SAP DN号','desc'=>'dnS4'),
                // 'item'=>array('type'=>'string','required'=>'true','name'=>'行明细','desc'=>'行明细'),
            ),
            'cancelCallback'=>array(
                'status'=>array('type'=>'string','required'=>'true','name'=>'状态','desc'=>'状态。可选值：E（ 处理失败），S（处理成功）'),
                'message'=>array('type'=>'string','required'=>'false','name'=>'信息','desc'=>'message'),
                'dnS4'=>array('type'=>'string','required'=>'true','name'=>'SAP DN号','desc'=>'dnS4'),
            ),
            'consignCallback'=>array(
                'status'=>array('type'=>'string','required'=>'true','name'=>'状态','desc'=>'状态。可选值：E（ 处理失败），S（处理成功）'),
                'message'=>array('type'=>'string','required'=>'false','name'=>'信息','desc'=>'message'),
                'dnS4'=>array('type'=>'string','required'=>'true','name'=>'SAP DN号','desc'=>'dnS4'),
            ),
        );

        return $params[$method];
    }

    public function description($method)
    {
        $desccription = array(
            'add' => array('name' => '创建ODN单', 'description' => '创建ODN单(大家电)(SAP发起的)'),
            'addCallback' => array('name' => 'ODN创建回调', 'description' => 'ODN创建回调(OMS发起的接收SAP回调的接口)'),
            'cancelCallback' => array('name' => 'ODN取消回调', 'description' => 'ODN取消回调'),
            'consignCallback' => array('name' => 'ODN发货回调', 'description' => 'ODN发货回调'),
        );
        return $desccription[$method];
    }
}