<?php
/**
 * 退货单OpenAPI参数定义
 * 类名: miele_openapi_params_v1_reship
 * 文件: app/miele/lib/openapi/params/v1/reship.php
 */
class miele_openapi_params_v1_returnorder extends openapi_api_params_abstract implements openapi_api_params_interface {

    public function checkParams($method, $params, &$sub_msg) {
        if(parent::checkParams($method, $params, $sub_msg)){
            return true;
        } else {
            return false;
        }
    }

    public function getAppParams($method) {
        $params = array(
            'addCallback' => array(
                'otherSystemSO' => array(
                    'type' => 'string',
                    'required' => 'true',
                    'name' => '退货单号',
                    'desc' => 'OMS系统退货单号'
                ),
                'sapSystemSO' => array(
                    'type' => 'string',
                    'required' => 'true',
                    'name' => 'SAP退货单号',
                    'desc' => 'SAP系统生成的退货单号'
                ),
                'status' => array(
                    'type' => 'string',
                    'required' => 'false',
                    'name' => '处理状态',
                    'desc' => 'SAP处理状态：success/fail'
                ),
                'message' => array(
                    'type' => 'string',
                    'required' => 'false',
                    'name' => '处理消息',
                    'desc' => 'SAP处理结果消息'
                ),
            ),
        );
        return $params[$method];
    }

    public function description($method) {
        $description = array(
            'addCallback' => array(
                'name' => '退货单创建回调',
                'description' => 'SAP系统处理完退货单后的回调通知'
            ),
        );
        return $description[$method];
    }
}
