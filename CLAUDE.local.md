# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# 永远使用中文回答

## 项目概览

**项目名称**: OMS系统 (Order Management System)  
**版本**: 6.0.0  
**框架**: Shopex OMS框架  
**技术栈**: PHP 8.0+, MySQL 8.0+, Swoole, Docker  
**主要功能**: 订单管理、仓库管理、物流管理、财务管理、客户关系管理等

## 高层架构和代码结构

### 核心架构特点
- **模块化设计**: 60+个独立业务模块，高内聚低耦合
- **可插拔架构**: 支持模块动态加载和卸载
- **事件驱动**: 基于服务注册和事件机制
- **任务队列**: 基于Swoole的异步任务处理
- **多语言支持**: 支持中文、英文、日文等多语言

### 目录结构
```
oms-system-beta/
├── app/                    # 核心应用模块 (60+个业务模块)
│   ├── base/              # 基础框架
│   ├── desktop/           # 桌面应用
│   ├── ome/               # 订单管理引擎
│   ├── wms/               # 仓库管理系统
│   ├── logistics/         # 物流管理
│   ├── finance/           # 财务管理
│   ├── crm/               # 客户关系管理
│   ├── miele/             # 美诺业务模块
│   └── taskmgr/           # 任务管理器
├── assets/                # 前端静态资源
├── config/                # 配置文件
├── data/                  # 数据文件
├── docs/                  # 项目文档
├── tests/                 # 测试用例
├── tools/                 # 工具脚本
├── docker/                # Docker配置
└── vendor/                # 第三方依赖
```

### 主要业务模块

#### 订单管理 (OME系列)
- **ome**: 订单管理引擎核心
- **omeanalysts**: 订单分析统计
- **omeauto**: 订单自动化处理
- **omecsv**: CSV导入导出
- **omedlyexport**: 延迟导出
- **omevirtualwms**: 虚拟仓库订单管理

#### 仓库管理 (WMS系列)
- **wms**: 仓库管理系统核心
- **wmsmgr**: 仓库管理工具
- **warehouse**: 仓库基础信息管理
- **inventorydepth**: 库存深度管理
- **iostock**: 出入库管理

#### 财务管理
- **finance**: 财务管理核心
- **financebase**: 财务基础模块
- **billcenter**: 账单中心
- **invoice**: 发票管理

#### 系统集成
- **openapi**: 开放API接口
- **erpapi**: ERP系统接口
- **qimen**: 阿里奇门接口
- **vop**: 供应商平台接口
- **ediws**: EDI Web服务

## 代码规范和最佳实践

### 自动加载机制
项目使用自定义自动加载器，位置在 `app/base/autoload.php`：

#### 类名解析规则
- **控制器类**: `{app_name}_ctl_{controller_path}` → `app/{app_name}/controller/{controller_path}.php`
- **模型类**: `{app_name}_mdl_{model_name}` → `app/{app_name}/model/{model_path}.php`
- **业务逻辑类**: `{app_name}_{class_name}` → `app/{app_name}/lib/{class_path}.php`

#### 示例
```php
// 控制器类
tongyioil_ctl_admin_region_relation → app/tongyioil/controller/admin/region/relation.php

// 模型类
tongyioil_mdl_region_relation → app/tongyioil/model/region/relation.php

// 业务逻辑类
tongyioil_region_relation → app/tongyioil/lib/region/relation.php
```

### 筛选扩展文件命名规范
```
app/{app_name}/lib/finder/extend/filter/{module}/{action}.php
```
类名格式：`{app_name}_finder_extend_filter_{module}_{action}`

### 服务注册机制
每个模块通过 `services.xml` 文件注册服务，支持事件驱动架构。

## 任务管理系统

### 任务队列配置
- **基于Swoole**: 使用进程池管理模式
- **白名单机制**: 任务定义在 `app/taskmgr/lib/whitelist/` 目录
- **自动重试**: 支持任务失败重试机制
- **多线程**: 支持多线程并发处理

### 常用任务类型
- `autochk`: 自动检查任务
- `autodly`: 自动发货任务
- `autorder`: 自动订单处理任务

## 测试框架

### 测试结构
```
tests/
├── integration/        # 集成测试
├── unit/              # 单元测试
├── erpapi/            # ERP API测试
├── miele/             # 美诺模块测试
└── logs/              # 测试日志
```

### 测试配置
- **PHPUnit**: 单元测试框架
- **集成测试**: 验证模块间交互
- **API测试**: 验证外部接口集成

## 数据库要求

### MySQL配置
- **版本**: 8.0+
- **重要**: sql_mode必须包含 `only_full_group_by`
- **字符集**: utf8mb4
- **存储引擎**: InnoDB

### 代码质量工具
- **PHPStan**: 静态代码分析 (level 2)
- **PHP_CodeSniffer**: 代码规范检查
- **PHPUnit**: 单元测试和集成测试

## 重要提醒

1. **Git提交规范**: 使用统一的提交风格，配置commit-msg钩子
2. **安全配置**: 禁止访问敏感目录 (.git, config, data等)
3. **错误处理**: 生产环境关闭错误显示，使用日志记录

## 相关文档

- **项目文档**: `docs/` 目录
- **开发指南**: `docs/cheatsheet/` 目录
- **API文档**: `docs/miele/api_reference.md`
- **业务场景**: `docs/miele/business_scenarios.md`
- **技术文档**: `docs/miele/technical_documentation.md`